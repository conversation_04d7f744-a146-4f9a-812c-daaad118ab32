<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <springProperty scope="context" name="LOG_LEVEL" source="logging.level.root" defaultValue="DEBUG"/>
    <springProperty scope="context" name="WORKSPACE" source="spider.workspace" defaultValue="/data/spider/logs"/>
    <!-- 控制台输出 -->
    <appender name="Stdout" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg  %n</pattern>
        </encoder>
    </appender>
    <!-- 输出日志文件 -->
    <appender name="File"  class="org.spiderflow.logback.SpiderFlowFileAppender">
        <file>${WORKSPACE}/logs/spider-flow.log</file>
        <append>true</append>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level %logger{50} - %msg%n</pattern>
        </encoder>
    </appender>
    <!-- WebSocket输出日志 -->
    <appender name="WebSocket" class="org.spiderflow.logback.SpiderFlowWebSocketAppender"/>
    <!-- 日志输出级别 -->
    <root level="${LOG_LEVEL}">
        <appender-ref ref="Stdout" />
        <appender-ref ref="File" />
        <appender-ref ref="WebSocket" />
    </root>
</configuration>
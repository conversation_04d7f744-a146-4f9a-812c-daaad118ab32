<?xml version="1.0"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.spiderflow</groupId>
		<artifactId>spider-flow</artifactId>
		<version>0.5.0</version>
	</parent>
	<artifactId>spider-flow-web</artifactId>
	<name>spider-flow-web</name>
	<url>https://gitee.com/jmxd/spider-flow/tree/master/spider-flow-web</url>
	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>
	<dependencies>
		<dependency>
			<groupId>org.spiderflow</groupId>
			<artifactId>spider-flow-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.spiderflow</groupId>
			<artifactId>spider-flow-redis</artifactId>
		</dependency>
		<dependency>
			<groupId>org.spiderflow</groupId>
			<artifactId>spider-flow-mongodb</artifactId>
		</dependency>
		<dependency>
			<groupId>org.spiderflow</groupId>
			<artifactId>spider-flow-selenium</artifactId>
		</dependency>
		<dependency>
			<groupId>org.spiderflow</groupId>
			<artifactId>spider-flow-ocr</artifactId>
		</dependency>
		<dependency>
			<groupId>org.spiderflow</groupId>
			<artifactId>spider-flow-oss</artifactId>
		</dependency>
		<dependency>
			<groupId>org.spiderflow</groupId>
			<artifactId>spider-flow-mailbox</artifactId>
		</dependency>
	</dependencies>
	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<finalName>spider-flow</finalName>
					<mainClass>org.spiderflow.SpiderApplication</mainClass>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>

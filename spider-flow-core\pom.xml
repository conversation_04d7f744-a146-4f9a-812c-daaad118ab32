<?xml version="1.0"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.spiderflow</groupId>
		<artifactId>spider-flow</artifactId>
		<version>0.5.0</version>
	</parent>
	<artifactId>spider-flow-core</artifactId>
	<name>spider-flow-core</name>
	<url>https://gitee.com/jmxd/spider-flow/tree/master/spider-flow-core</url>
	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>
	<dependencies>
		<dependency>
			<groupId>org.spiderflow</groupId>
			<artifactId>spider-flow-api</artifactId>
		</dependency>
		<!-- GraalVM Polyglot JavaScript 引擎 -->
		<dependency>
			<groupId>org.graalvm.polyglot</groupId>
			<artifactId>polyglot</artifactId>
			<version>24.1.1</version>
		</dependency>
		<dependency>
			<groupId>org.graalvm.polyglot</groupId>
			<artifactId>js</artifactId>
			<version>24.1.1</version>
		</dependency>
	</dependencies>
</project>

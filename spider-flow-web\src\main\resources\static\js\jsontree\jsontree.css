/*
 * JSON Tree Viewer
 * http://github.com/summerstyle/jsonTreeViewer
 *
 * Copyright 2017 <PERSON> (http://iamvera.com)
 * Released under the MIT license (LICENSE.txt)
 */

/* Background for the tree. May use for <body> element */
.jsontree_bg {
    background: #FFF;
}

/* Styles for the container of the tree (e.g. fonts, margins etc.) */
.jsontree_tree {
    margin-left: 30px;
    font-family: 'PT Mono', monospace;
    font-size: 14px;
}

/* Styles for a list of child nodes */
.jsontree_child-nodes {
    display: none;
    margin-left: 35px; 
    margin-bottom: 5px;
    line-height: 2;
}
.jsontree_node_expanded > .jsontree_value-wrapper > .jsontree_value > .jsontree_child-nodes {
    display: block;
}

/* Styles for labels */
.jsontree_label-wrapper {
    float: left;
    margin-right: 8px;
}
.jsontree_label {
    font-weight: normal;
    vertical-align: top;
    color: #000;
    position: relative;
    padding: 1px;
    border-radius: 4px;
    cursor: default;
}
.jsontree_node_marked > .jsontree_label-wrapper > .jsontree_label {
    background: #fff2aa;
}

/* Styles for values */
.jsontree_value-wrapper {
    display: block;
    overflow: hidden;
}
.jsontree_node_complex > .jsontree_value-wrapper {
    overflow: inherit;
}
.jsontree_value { 
    vertical-align: top;
    display: inline;
}
.jsontree_value_null {
    color: #777;
    font-weight: bold;
}
.jsontree_value_string {
    color: #025900;
    font-weight: bold;
}
.jsontree_value_number {
    color: #000E59;
    font-weight: bold;
}
.jsontree_value_boolean {
    color: #600100;
    font-weight: bold;
}

/* Styles for active elements */
.jsontree_expand-button {
    position: absolute;
    top: 3px;
    left: -15px;
    display: block;
    width: 11px;
    height: 11px;
    background-image: url('icons.svg');
}
.jsontree_node_expanded > .jsontree_label-wrapper > .jsontree_label > .jsontree_expand-button {
    background-position: 0 -11px;
}
.jsontree_show-more {
    cursor: pointer;
}
.jsontree_node_expanded > .jsontree_value-wrapper > .jsontree_value > .jsontree_show-more {
    display: none;
}
.jsontree_node_empty > .jsontree_label-wrapper > .jsontree_label > .jsontree_expand-button,
.jsontree_node_empty > .jsontree_value-wrapper > .jsontree_value > .jsontree_show-more {
    display: none !important;
}
.jsontree_node_complex > .jsontree_label-wrapper > .jsontree_label {
    cursor: pointer;
}
.jsontree_node_empty > .jsontree_label-wrapper > .jsontree_label {
    cursor: default !important;
}
<div class="layui-tab layui-tab-fixed layui-tab-brief">
  <ul class="layui-tab-title">
    <li class="layui-this">配置</li>
  </ul>
  <div class="layui-tab-content">
    <div class="layui-tab-item layui-show">
    	<form class="layui-form editor-form-node">
			<div class="layui-row">
				<div class="layui-col-md4">
					<div class="layui-form-item">
						<label class="layui-form-label">节点名称</label>
						<div class="layui-input-block">
							<input type="text" name="value" placeholder="请输入节点名称" value="{{=d.value}}" autocomplete="off" class="layui-input">
						</div>
					</div>
				</div>
				<div class="layui-col-md4">
					<div class="layui-form-item">
						<label class="layui-form-label">次数或集合</label>
						<div class="layui-input-block" codemirror="loopCount" placeholder="请输入循环次数或集合" data-value="{{=d.data.object.loopCount}}"></div>
					</div>
				</div>
				<div class="layui-col-md4">
					<div class="layui-form-item">
						<label class="layui-form-label">循环变量</label>
						<div class="layui-input-block">
							<input type="text" name="loopItem" placeholder="请输入循环变量(默认item)" autocomplete="off" class="layui-input input-default" value="{{=d.data.object.loopItem}}">
						</div>
					</div>
				</div>
			</div>
			<div class="layui-row">
				<div class="layui-col-md4">
					<div class="layui-form-item">
						<label class="layui-form-label">下标变量</label>
						<div class="layui-input-block">
							<input type="text" name="loopVariableName" placeholder="请输入下标变量" autocomplete="off" class="layui-input input-default" value="{{=d.data.object.loopVariableName}}">
						</div>
					</div>
				</div>
				<div class="layui-col-md4">
					<div class="layui-form-item">
						<label class="layui-form-label">开始位置</label>
						<div class="layui-input-block" codemirror="loopStart" placeholder="请输入循环次数" data-value="{{=d.data.object.loopStart || '0'}}"></div>
					</div>
				</div>
				<div class="layui-col-md4">
					<div class="layui-form-item">
						<label class="layui-form-label">结束位置</label>
						<div class="layui-input-block" codemirror="loopEnd" placeholder="请输入循环次数" data-value="{{=d.data.object.loopEnd || -1}}"></div>
					</div>
				</div>
			</div>
    	</form>
    </div>
  </div>
</div>
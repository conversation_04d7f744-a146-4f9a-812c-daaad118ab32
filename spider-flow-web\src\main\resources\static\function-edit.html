<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>DataSource</title>
	<link rel="stylesheet" href="js/layui/css/layui.css" />
	<link rel="stylesheet" href="css/layui-blue.css" />
	<link rel="stylesheet" href="js/codemirror/codemirror.css" />
	<link rel="stylesheet" href="js/codemirror/dracula.css" />
	<script type="text/javascript" src="js/layui/layui.all.js" ></script>
	<script type="text/javascript" src="js/common.js" ></script>
	<script type="text/javascript" src="js/codemirror/codemirror.js" ></script>
	<script type="text/javascript" src="js/codemirror/javascript.js" ></script>
	<style type="text/css">
		html,body{
			width:100%;
		}
		.layui-form{
			width : 700px;
			margin-top:10px;
		}
		.layui-form-label{
			width : 140px;
		}
		.layui-input-block{
			margin-left : 170px;
		}
		.btns-submit{
			text-align : center;
		}
	</style>
</head>
<body>
	<form class="layui-form" autocomplete="off" lay-filter="form" style="width:auto !important;padding:20px;">
		<div class="layui-row">
			<div class="layui-col-md6">
				<div class="layui-form-item">
					<label class="layui-form-label">函数名称</label>
					<div class="layui-input-block">
						<input type="text" name="name" placeholder="请输入函数名" autocomplete="off" class="layui-input" lay-verify="required"/>
					</div>
				</div>
			</div>
			<div class="layui-col-md6">
				<div class="layui-form-item">
					<label class="layui-form-label">函数入参</label>
					<div class="layui-input-block">
						<input type="text" name="parameter" placeholder="请输入函数入参,多个用逗号隔开" autocomplete="off" class="layui-input"/>
					</div>
				</div>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">脚本</label>
			<div class="layui-input-block" style="height: 450px">
				<textarea name="script" id="script"></textarea>
			</div>
		</div>
   		<div class="btns-submit">
			<button class="layui-btn layui-btn-normal" lay-submit lay-filter="save">保存</button>
			<button class="layui-btn layui-btn-primary btn-return" type="button" onclick="history.go(-1);">返回</button>
		</div>
	</form>
	<script type="text/javascript">
		var editor = CodeMirror.fromTextArea(document.getElementById("script"), {
			lineNumbers: true,
			matchBrackets: true,
			continueComments: "Enter"
		});
		editor.setOption('theme','dracula')
		var $ = layui.$;
		var fId = getQueryString('id');
		if(fId){
			$.ajax({
				url : 'function/get',
				data : {
					id : fId
				},
				success : function(data) {
					layui.form.val('form', data);
					if(data.script){
						editor.setValue(data.script);
					}
				}
			});
		}
		layui.form.on('submit(save)',function(){
			$.ajax({
				url : 'function/save',
				type : 'post',
				data : {
					id : fId,
					name : $("input[name=name]").val(),
					parameter : $("input[name=parameter]").val(),
					script : editor.getValue()
				},
				success : function(data){
					if(data){
						var message = data.replace(/\n/g,'<br>').replace(/ /g,'&nbsp;').replace(/\t/g,'&nbsp;&nbsp;&nbsp;&nbsp;');
						layui.layer.alert('<div style="font-weight: bold;font-family:Consolas;font-size:12px;">' + message + '</div>',{title : '保存出错',area:['800px','400px']});
					}else{
						layui.layer.msg('保存成功',{
							time : 800
						},function(){
							location.href = 'functions.html';
						})
					}
				},
				error : function(){
					layui.layer.msg('请求失败');
				}
			})
			return false;
		})
	</script>
</body>
</html>
<div class="layui-tab layui-tab-fixed layui-tab-brief">
  <ul class="layui-tab-title">
    <li class="layui-this">配置</li>
  </ul>
  <div class="layui-tab-content">
    <div class="layui-tab-item layui-show">
    	<form class="layui-form editor-form-node">
			<div class="layui-row">
				<div class="layui-col-md4">
					<div class="layui-form-item">
						<label class="layui-form-label">节点名称</label>
						<div class="layui-input-block">
							<input type="text" name="value" placeholder="请输入节点名称" value="{{=d.value}}" autocomplete="off" class="layui-input">
						</div>
					</div>
				</div>
				<div class="layui-col-md4">
					<div class="layui-form-item">
						<label class="layui-form-label">循环变量</label>
						<div class="layui-input-block">
							<input type="text" name="loopVariableName" placeholder="请输入循环变量" autocomplete="off" class="layui-input input-default" value="{{=d.data.object.loopVariableName}}">
						</div>
					</div>
				</div>
				<div class="layui-col-md4">
					<div class="layui-form-item">
						<label class="layui-form-label">循环次数</label>
						<div class="layui-input-block" codemirror="loopCount" placeholder="请输入循环次数" data-value="{{=d.data.object.loopCount}}"></div>
					</div>
				</div>
			</div>
			<table class="layui-table" id="spider-variable" data-cell="{{=d.cell.id}}" data-keys="variable-name,variable-value,variable-description"></table>
    		<div class="layui-form-item">
    			<div class="layui-input-inline">
	    			<button class="layui-btn table-row-add" type="button" for="spider-variable">添加一个变量</button>
	    		</div>
	    	</div>
    	</form>
    </div>
  </div>
</div>

<script>
	$(function(){
		function renderSpiderVariable(data){
			layui.table.render({
				elem : '#spider-variable',
				limit: 50,
				cols : [[{
					title : '变量名',
					width : 150,
					templet : '#variable-name-tmpl'
				},{
					title : '变量值',
					minWidth : 400,
					templet : '#variable-value-tmpl'
				},{
					title : '描述',
					width : 250,
					templet : '#variable-description-tmpl'
				},{
					title : '操作',
					width : 120,
					align : 'center',
					templet : '#common-operation'
				}]],
				data : data,
				text : {
					none : '暂未设置变量'
				}
			})
		}
		renderSpiderVariable(getCellData({{d.cell.id}},$("#spider-variable").data('keys').split(",")));
	});
</script>
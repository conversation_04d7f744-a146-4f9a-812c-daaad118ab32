<div class="layui-tab layui-tab-fixed layui-tab-brief">
  <ul class="layui-tab-title">
    <li class="layui-this">基本配置</li>
    <li>参数</li>
	<li><PERSON><PERSON></li>
    <li>Header</li>
    <li>Body</li>
  </ul>
  <div class="layui-tab-content editor-form-node">
    <div class="layui-tab-item layui-show">
    	<form class="layui-form">
			<div class="layui-row">
				<div class="layui-col-md4">
					<div class="layui-form-item">
						<label class="layui-form-label">节点名称</label>
						<div class="layui-input-block">
							<input type="text" name="value" placeholder="请输入节点名称" value="{{=d.value}}" autocomplete="off" class="layui-input">
						</div>
					</div>
				</div>
				<div class="layui-col-md4">
					<div class="layui-form-item">
						<label class="layui-form-label">循环变量</label>
						<div class="layui-input-block">
							<input type="text" name="loopVariableName" placeholder="请输入循环变量" autocomplete="off" class="layui-input input-default" value="{{=d.data.object.loopVariableName}}">
						</div>
					</div>
				</div>
				<div class="layui-col-md4">
					<div class="layui-form-item">
						<label class="layui-form-label">循环次数</label>
						<div class="layui-input-block" codemirror="loopCount" placeholder="请输入循环次数" data-value="{{=d.data.object.loopCount}}"></div>
					</div>
				</div>
			</div>
			<div class="layui-row">
				<div class="layui-col-md2">
					<div class="layui-form-item">
						<label class="layui-form-label">请求方法</label>
						<div class="layui-input-block">
							<select name="method">
								<option value="GET" {{d.data.object.method == 'GET' ? 'selected':''}}>GET</option>
								<option value="POST" {{d.data.object.method == 'POST' ? 'selected':''}}>POST</option>
								<option value="PUT" {{d.data.object.method == 'PUT' ? 'selected':''}}>PUT</option>
								<option value="DELETE" {{d.data.object.method == 'DELETE' ? 'selected':''}}>DELETE</option>
								<option value="PATCH" {{d.data.object.method == 'PATCH' ? 'selected':''}}>PATCH</option>
								<option value="HEAD" {{d.data.object.method == 'HEAD' ? 'selected':''}}>HEAD</option>
								<option value="OPTIONS" {{d.data.object.method == 'OPTIONS' ? 'selected':''}}>OPTIONS</option>
								<option value="TRACE" {{d.data.object.method == 'TRACE' ? 'selected':''}}>TRACE</option>
							</select>
						</div>
					</div>
				</div>
				<div class="layui-col-md10">
					<div class="layui-form-item">
						<label class="layui-form-label">URL</label>
						<div class="layui-input-block" codemirror="url" data-value="{{=d.data.object.url}}"></div>
					</div>
				</div>
			</div>
			<div class="layui-row">
				<div class="layui-col-md4">
					<div class="layui-form-item">
						<label class="layui-form-label">延迟时间</label>
						<div class="layui-input-block">
							<input type="text" name="sleep" placeholder="请输入延迟时间" autocomplete="off" class="layui-input input-default" value="{{=d.data.object.sleep}}">
						</div>
					</div>
				</div>
				<div class="layui-col-md4">
					<div class="layui-form-item">
						<label class="layui-form-label">超时时间</label>
						<div class="layui-input-block">
							<input type="text" name="timeout" placeholder="请输入超时时间" autocomplete="off" class="layui-input input-default" value="{{=d.data.object.timeout}}">
						</div>
					</div>
				</div>
				<div class="layui-col-md4">
					<div class="layui-form-item">
						<label class="layui-form-label">页面编码</label>
						<div class="layui-input-block">
							<input type="text" name="response-charset" placeholder="请输入页面编码" autocomplete="off" class="layui-input input-default" value="{{=d.data.object['response-charset']}}">
						</div>
					</div>
				</div>
			</div>
			<div class="layui-row">
				<div class="layui-col-md4">
					<div class="layui-form-item">
						<label class="layui-form-label">重试次数</label>
						<div class="layui-input-block">
							<input type="text" name="retryCount" placeholder="请输入重试次数" autocomplete="off" class="layui-input input-default" value="{{=d.data.object.retryCount}}">
						</div>
					</div>
				</div>
				<div class="layui-col-md4">
					<div class="layui-form-item">
						<label class="layui-form-label">重试间隔</label>
						<div class="layui-input-block">
							<input type="text" name="retryInterval" placeholder="请输入重试间隔" autocomplete="off" class="layui-input input-default" value="{{=d.data.object.retryInterval}}">
						</div>
					</div>
				</div>
			</div>
			<div class="layui-row">
				<div class="layui-col-md4">
					<div class="layui-form-item">
						<label class="layui-form-label">代理</label>
						<div class="layui-input-block" placeholder="host:port" codemirror="proxy" data-value="{{=d.data.object.proxy}}"></div>
					</div>
				</div>
				<div class="layui-col-md8">
					<div class="layui-form-item">
						<label class="layui-form-label">请求设置</label>
						<div class="layui-input-block">
							<input type="checkbox" title="跟随重定向" value="follow-redirect" lay-skin="primary" {{d.data.object['follow-redirect'] == '0' ? '' : 'checked'}}/>
							<input type="checkbox" title="TLS证书验证" value="tls-validate" lay-skin="primary" {{d.data.object['tls-validate'] == '0' ? '' : 'checked'}}/>
							<input type="checkbox" title="自动管理Cookie" value="cookie-auto-set" lay-skin="primary" {{d.data.object['cookie-auto-set'] == '0' ? '' : 'checked'}}/>
							<input type="checkbox" title="自动去重" value="repeat-enable" lay-skin="primary" {{d.data.object['repeat-enable'] == '1' ? 'checked' : ''}}/>
						</div>
					</div>
				</div>
			</div>

    	</form>
    </div>
    <div class="layui-tab-item">
    	<form class="layui-form">
			<table class="layui-table" id="request-parameter" data-cell="{{=d.cell.id}}" data-keys="parameter-name,parameter-value,parameter-description"></table>
	   		<div id="addParamterBtn" class="layui-form-item">
	   			<div class="layui-input-inline">
	    			<button class="layui-btn table-row-add" type="button" for="request-parameter">添加一个参数</button>
	    		</div>
				<div class="layui-input-inline">
					<button class="layui-btn parameter-batch" type="button" for="request-parameter">批量设置参数</button>
				</div>
			</div>
    	</form>
    </div>
	  <div class="layui-tab-item">
		  <form class="layui-form">
			  <table class="layui-table" id="request-cookie" data-cell="{{=d.cell.id}}" data-keys="cookie-name,cookie-value,cookie-description"></table>
			  <div id="addCookieBtn" class="layui-form-item">
				  <div class="layui-input-inline">
					  <button class="layui-btn table-row-add" type="button" for="request-cookie">添加一个Cookie</button>
				  </div>
				  <div class="layui-input-inline">
					  <button class="layui-btn cookie-batch" type="button" for="request-cookie">批量设置Cookie</button>
				  </div>
			  </div>
		  </form>
	  </div>
    <div class="layui-tab-item">
    	<form class="layui-form">
			<table class="layui-table" id="request-header" data-cell="{{=d.cell.id}}" data-keys="header-name,header-value,header-description"></table>
	   		<div id="addHeaderBtn" class="layui-form-item">
	   			<div class="layui-input-inline">
	    			<button class="layui-btn table-row-add" type="button" for="request-header">添加一个Header</button>
	    		</div>
				<div class="layui-input-inline">
					<button class="layui-btn header-batch" type="button" for="request-header">批量设置Header</button>
				</div>
			</div>
	    </form>
    </div>
  	
  	<div class="layui-tab-item">
    	<form class="layui-form">
    		<div class="layui-form-item">
    			<label class="layui-form-label">类型</label>
    			<div class="layui-input-block">
    				<select name="body-type" lay-filter="bodyType">
	    				<option value="none" {{d.data.object['body-type'] == 'none' ? 'selected':''}}>none</option>
	   					<option value="form-data" {{d.data.object['body-type'] == 'form-data' ? 'selected':''}}>form-data</option>
	   					<option value="raw" {{d.data.object['body-type'] == 'raw' ? 'selected':''}}>raw</option>
   					</select>
    			</div>
    		</div>
    		<div  class="form-body-raw" {{d.data.object['body-type'] != 'raw' ? 'style="display:none;"':''}}>
	    		<div class="layui-form-item">
	    			<label class="layui-form-label">Content-Type</label>
	    			<div class="layui-input-block">
	    				<select name="body-content-type">
		    				<option value="text/plain" {{d.data.object['body-content-type'] == 'text/plain' ? 'selected':''}}>text/plain</option>
		   					<option value="application/json" {{d.data.object['body-content-type'] == 'application/json' ? 'selected':''}}>application/json</option>
	   					</select>
	    			</div>
	    		</div>
	    		<div class="layui-form-item">
	    			<label class="layui-form-label">内容</label>
	    			<div class="layui-input-block" style="height:200px;" placeholder="请输入内容" codemirror="request-body" data-value="{{=d.data.object['request-body']}}"></div>
	    		</div>
    		</div>
    		<div class="form-body-form-data" {{d.data.object['body-type'] != 'form-data' ? 'style="display:none;"':''}}>
	  			<table class="layui-table" id="body-parameter" data-cell="{{=d.cell.id}}" data-keys="parameter-form-name,parameter-form-value,parameter-form-type,parameter-form-filename,parameter-form-description"></table>
	    		<div class="layui-form-item">
		   			<div class="layui-input-inline">
		    			<button class="layui-btn table-row-add" type="button" for="body-parameter">添加一个参数</button>
		    		</div>
		    	</div>
    		</div>
	    </form>
    </div>
  </div>
</div>
<script>
	$(function(){
		function renderRequestParameter(data){
			layui.table.render({
				elem : '#request-parameter',
				limit: 50,
				cols : [[{
					title : '参数名',
					width : 150,
					templet : '#parameter-name-tmpl'
				},{
					title : '参数值',
					templet : '#parameter-value-tmpl'
				},{
					title : '参数描述',
					width : 250,
					templet : '#parameter-description-tmpl'
				},{
					title : '操作',
					width : 120,
					align : 'center',
					templet : '#common-operation'
				}]],
				data : data,
				text : {
					none : '暂未设置参数'
				}
			})
		}
		function renderRequestCookie(data){
			layui.table.render({
				elem : '#request-cookie',
				limit: 50,
				cols : [[{
					title : 'Cookie名',
					width : 150,
					templet : '#cookie-name-tmpl'
				},{
					title : 'Cookie值',
					templet : '#cookie-value-tmpl'
				},{
					title : '描述',
					width : 250,
					templet : '#cookie-description-tmpl'
				},{
					title : '操作',
					width : 120,
					align : 'center',
					templet : '#common-operation'
				}]],
				data : data,
				text : {
					none : '暂未设置Cookie'
				}
			})
		}

		function renderRequestHeader(data){
			layui.table.render({
				elem : '#request-header',
				limit: 50,
				cols : [[{
					title : 'Header名',
					width : 150,
					templet : '#header-name-tmpl'
				},{
					title : 'header值',
					minWidth : 400,
					templet : '#header-value-tmpl'
				},{
					title : '描述',
					width : 250,
					templet : '#header-description-tmpl'
				},{
					title : '操作',
					width : 120,
					align : 'center',
					templet : '#common-operation'
				}]],
				data : data,
				text : {
					none : '暂未设置Header'
				}
			})
		}
		function renderBodyParameter(data){
			layui.table.render({
				elem : '#body-parameter',
				cols : [[{
					title : '参数名',
					width : 150,
					templet : '#parameter-from-name-tmpl'
				},{
					title : '参数类型',
					width : 85,
					templet : '#parameter-from-type-tmpl'
				},{
					title : '文件名',
					width : 195,
					templet : '#parameter-from-filename-tmpl'
				},{
					title : '参数值',
					templet : '#parameter-from-value-tmpl'
				},{
					title : '参数描述',
					width : 250,
					templet : '#parameter-from-description-tmpl'
				},{
					title : '操作',
					width : 120,
					align : 'center',
					templet : '#common-operation'
				}]],
				data : data,
				text : {
					none : '暂未设置参数'
				}
			})
		}
		renderRequestParameter(getCellData({{d.cell.id}},$("#request-parameter").data('keys').split(",")));
		renderRequestCookie(getCellData({{d.cell.id}},$("#request-cookie").data('keys').split(",")));
		renderRequestHeader(getCellData({{d.cell.id}},$("#request-header").data('keys').split(",")));
		renderBodyParameter(getCellData({{d.cell.id}},$("#body-parameter").data('keys').split(",")));
	});
</script>
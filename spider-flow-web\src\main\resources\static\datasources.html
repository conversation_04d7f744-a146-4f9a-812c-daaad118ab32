<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>DataSource</title>
	<link rel="stylesheet" href="js/layui/css/layui.css" />
	<link rel="stylesheet" href="css/layui-blue.css" />
	<script type="text/javascript" src="js/layui/layui.all.js" ></script>
</head>
<body style="padding:5px;">
<a class="layui-btn layui-btn-sm layui-btn-normal" href="datasource-edit.html"><i class="layui-icon">&#xe654;</i> 添加数据源</a>
<hr>
<table class="layui-table" id="table" lay-filter="table"></table>
<script>
	var $ = layui.$;
	var $table = layui.table.render({
		id : 'table',
		elem : '#table',
		url : 'datasource/list',
		page : true,
		parseData : function(resp){
			return {
				code : 0,
				data : resp.records,
				count : resp.total
			}
		},
		cols : [[{
			title : '序号',
			width : 60,
			type : 'numbers',
			align : 'center'
		},{
			title : '数据源名称',
			field : 'name'
		},{
			title : '驱动',
			field : 'driverClassName',
		},{
			title : '创建时间',
			width : 160,
			field : 'createDate',
			align : 'center'
		},{
			title : '操作',
			width : 120,
			align : 'center',
			templet : '#buttons'
		}]]
	})
	$("body").on('click','.btn-remove',function(){
		var id = $(this).data('id');
		layui.layer.confirm('您确定要删除此数据源吗？',{
			title : '删除'
		},function(index){
			$table.reload();
			$.ajax({
				url : 'datasource/remove',
				data : {
					id : id
				},
				success : function(){
					layui.layer.msg('删除成功',{time : 500},function(){
						$table.reload();
					})
				},
				error : function(){
					layui.layer.msg('删除失败')
				}
			})
			layui.layer.close(index);
		})
	}).on('click','.btn-edit',function(){
		location.href = 'datasource-edit.html?id=' + $(this).data('id');
	})
</script>
<script type="text/html" id="buttons">
	<a class="layui-btn layui-btn-sm btn-edit" data-id="{{d.id}}">编辑</a>
	<a class="layui-btn layui-btn-sm btn-remove" data-id="{{d.id}}">删除</a>
</script>
</body>
</html>
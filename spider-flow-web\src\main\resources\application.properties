server.port=8088

logging.level.root=INFO
#logging.level.org.spiderflow=DEBUG
#平台最大线程数
spider.thread.max=64
#单任务默认最大线程数
spider.thread.default=8
#设置为true时定时任务才生效
spider.job.enable=false
#爬虫任务的工作空间
spider.workspace=/data/spider
#布隆过滤器默认容量
spider.bloomfilter.capacity=1000000
#布隆过滤器默认容错率
spider.bloomfilter.error-rate=0.0001

#死循环检测(节点执行次数超过该值时认为是死循环)默认值为5000
spider.detect.dead-cycle=5000

spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8
spring.jackson.serialization.fail_on_empty_beans=false

spring.mvc.favicon.enabled=false

spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.username=root
spring.datasource.password="021026"
spring.datasource.url=*************************************************************************************************************

#JavaMailSender 邮件发送的配置
spring.mail.protocol=smtp
spring.mail.host=smtp.qq.com
spring.mail.port=465
spring.mail.username=<EMAIL>
spring.mail.password=xxxx
spring.mail.default-encoding=UTF-8
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true
spring.mail.properties.mail.smtp.socketFactory.class=javax.net.ssl.SSLSocketFactory
spring.mail.properties.mail.smtp.socketFactory.port=465
spring.mail.properties.mail.smtp.socketFactory.fallback=false

spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration,org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration

#selenium 插件配置

#设置chrome的WebDriver驱动路径，下载地址：http://npm.taobao.org/mirrors/chromedriver/，注意版本问题
selenium.driver.chrome=D:/driver/chromedriver.exe
#设置fireFox的WebDriver驱动路径，下载地址：https://github.com/mozilla/geckodriver/releases
selenium.driver.firefox=D:/driver/geckodriver.exe

#爬虫通知相关内容配置,可使用SpiderFlow中的变量名和以下变量名:currentDate:当前发送时间
spider.notice.subject=spider-flow流程通知
spider.notice.content.start=流程开始执行：{name}，开始时间：{currentDate}
spider.notice.content.end=流程执行完毕：{name}，结束时间：{currentDate}
spider.notice.content.exception=流程发生异常：{name}，异常时间：{currentDate}
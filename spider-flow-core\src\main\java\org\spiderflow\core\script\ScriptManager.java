package org.spiderflow.core.script;

import org.graalvm.polyglot.Context;
import org.graalvm.polyglot.Engine;
import org.graalvm.polyglot.Value;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.spiderflow.core.expression.ExpressionTemplate;
import org.spiderflow.core.expression.ExpressionTemplateContext;

import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

public class ScriptManager {

    private static Logger logger = LoggerFactory.getLogger(ScriptManager.class);

    private static ScriptEngine scriptEngine;

    // GraalVM Polyglot 引擎和上下文
    private static Engine graalEngine;
    private static Context graalContext;

    private static Set<String> functions = new HashSet<>();

    private static ReadWriteLock lock = new ReentrantReadWriteLock();

    public static void setScriptEngine(ScriptEngine engine){
        scriptEngine = engine;

        // 初始化 GraalVM 引擎和上下文
        if (graalEngine == null) {
            graalEngine = Engine.create();
        }
        if (graalContext == null) {
            graalContext = Context.newBuilder("js")
                    .engine(graalEngine)
                    .allowAllAccess(true)
                    .build();
        }

        StringBuffer script = new StringBuffer();
        script.append("var ExpressionTemplate = Java.type('")
                .append(ExpressionTemplate.class.getName())
                .append("');")
                .append("var ExpressionTemplateContext = Java.type('")
                .append(ExpressionTemplateContext.class.getName())
                .append("');")
                .append("function _eval(expression) {")
                .append("return ExpressionTemplate.create(expression).render(ExpressionTemplateContext.get());")
                .append("}");
        try {
            // 同时在传统引擎和 GraalVM 中注册函数
            if (scriptEngine != null) {
                scriptEngine.eval(script.toString());
            }
            graalContext.eval("js", script.toString());
        } catch (Exception e) {
            logger.error("注册_eval函数失败",e);
        }
    }

    public static void clearFunctions(){
        functions.clear();
    }

    /**
     * 清理 GraalVM 资源
     */
    public static void cleanup() {
        try {
            if (graalContext != null) {
                graalContext.close();
                graalContext = null;
            }
            if (graalEngine != null) {
                graalEngine.close();
                graalEngine = null;
            }
        } catch (Exception e) {
            logger.warn("清理 GraalVM 资源时发生错误", e);
        }
    }

    public static ScriptEngine createEngine(){
        // 优先尝试使用 GraalVM JavaScript 引擎
        ScriptEngine engine = new ScriptEngineManager().getEngineByName("graal.js");
        if (engine == null) {
            // 如果 GraalVM 不可用，尝试使用 Nashorn（如果可用）
            engine = new ScriptEngineManager().getEngineByName("nashorn");
        }
        return engine;
    }

    public static void lock(){
        lock.writeLock().lock();
    }

    public static void unlock(){
        lock.writeLock().unlock();
    }

    public static void registerFunction(ScriptEngine engine,String functionName,String parameters,String script){
        try {
            String functionScript = concatScript(functionName,parameters,script);
            // 在传统引擎中注册
            if (engine != null) {
                engine.eval(functionScript);
            }
            // 在 GraalVM 中注册
            if (graalContext != null) {
                graalContext.eval("js", functionScript);
            }
            functions.add(functionName);
            logger.info("注册自定义函数{}成功",functionName);
        } catch (Exception e) {
            logger.warn("注册自定义函数{}失败",functionName,e);
        }
    }

    private static String concatScript(String functionName,String parameters,String script){
        StringBuffer scriptBuffer = new StringBuffer();
        scriptBuffer.append("function ")
                .append(functionName)
                .append("(")
                .append(parameters == null ? "" : parameters)
                .append("){")
                .append(script)
                .append("}");
        return scriptBuffer.toString();
    }

    public static boolean containsFunction(String functionName){
        try {
            lock.readLock().lock();
            return functions.contains(functionName);
        } finally {
            lock.readLock().unlock();
        }
    }

    public static void validScript(String functionName,String parameters,String script) throws Exception {
        String functionScript = concatScript(functionName,parameters,script);
        // 优先使用 GraalVM 验证
        if (graalContext != null) {
            graalContext.eval("js", functionScript);
        } else {
            // 回退到传统引擎
            ScriptEngine engine = createEngine();
            if (engine != null) {
                engine.eval(functionScript);
            } else {
                throw new Exception("无可用的 JavaScript 引擎");
            }
        }
    }

    public static Object eval(ExpressionTemplateContext context, String functionName, Object ... args) throws ScriptException, NoSuchMethodException {
        if("_eval".equals(functionName)){
            if(args == null || args.length != 1){
                throw new ScriptException("_eval必须要有一个参数");
            }else{
                return ExpressionTemplate.create(args[0].toString()).render(context);
            }
        }

        try{
            lock.readLock().lock();

            // 优先使用 GraalVM 执行
            if (graalContext != null) {
                Value function = graalContext.getBindings("js").getMember(functionName);
                if (function != null && function.canExecute()) {
                    Value result = function.execute(args);
                    return convertGraalValue(result);
                }
            }

            // 回退到传统引擎
            if(scriptEngine != null && scriptEngine instanceof Invocable){
                return convertObject(((Invocable) scriptEngine).invokeFunction(functionName, args));
            }

            throw new NoSuchMethodException(functionName);
        } finally{
            lock.readLock().unlock();
        }
    }

    /**
     * 转换 GraalVM Value 对象为 Java 对象
     */
    private static Object convertGraalValue(Value value) {
        if (value == null) {
            return null;
        }

        if (value.isNull()) {
            return null;
        }

        if (value.isBoolean()) {
            return value.asBoolean();
        }

        if (value.isNumber()) {
            if (value.fitsInInt()) {
                return value.asInt();
            } else if (value.fitsInLong()) {
                return value.asLong();
            } else if (value.fitsInDouble()) {
                return value.asDouble();
            }
        }

        if (value.isString()) {
            return value.asString();
        }

        if (value.hasArrayElements()) {
            long size = value.getArraySize();
            Object[] array = new Object[(int) size];
            for (int i = 0; i < size; i++) {
                array[i] = convertGraalValue(value.getArrayElement(i));
            }
            return array;
        }

        if (value.isDate()) {
            return Date.from(value.asDate().atStartOfDay().atZone(java.time.ZoneId.systemDefault()).toInstant());
        }

        // 对于其他类型，尝试转换为字符串
        return value.toString();
    }

    /**
     * 转换传统 ScriptEngine 对象（保持向后兼容）
     */
    private static Object convertObject(Object object){
        // 由于移除了 Nashorn 依赖，这里只做基本转换
        if (object instanceof Date) {
            return object;
        }

        // 对于数组类型的处理
        if (object != null && object.getClass().isArray()) {
            return object;
        }

        return object;
    }
}
